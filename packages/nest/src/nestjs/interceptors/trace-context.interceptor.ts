import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';
import {
  context as otelContext,
  trace as otelTrace,
  propagation,
  SpanKind,
  SpanStatusCode
} from '@opentelemetry/api';
import { ReqHeader } from '../../types';

/**
 * Trace Context 拦截器
 * 从HTTP请求头中解析trace上下文，并创建子span
 */
@Injectable()
export class TraceContextInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // 过滤mq请求
    if (isRabbitContext(context)) {
      return next.handle();
    }

    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();

    // 过滤健康检查等路径
    const excludePaths = ['/', '/health'];
    if (excludePaths.includes(request.url)) {
      return next.handle();
    }

    // 从请求头中提取trace context
    const traceHeaders: Record<string, string> = {};

    // 支持uber-trace-id (Jaeger格式)
    const uberTraceId = request.headers[ReqHeader.UberTraceId];
    if (uberTraceId && typeof uberTraceId === 'string') {
      traceHeaders['uber-trace-id'] = uberTraceId;
    }

    // 支持标准的W3C trace context
    const traceParent = request.headers['traceparent'];
    if (traceParent && typeof traceParent === 'string') {
      traceHeaders['traceparent'] = traceParent;
    }

    const traceState = request.headers['tracestate'];
    if (traceState && typeof traceState === 'string') {
      traceHeaders['tracestate'] = traceState;
    }

    // 如果没有找到任何trace头，直接继续
    if (Object.keys(traceHeaders).length === 0) {
      return next.handle();
    }

    try {
      // 使用propagation API从headers中提取context
      const parentContext = propagation.extract(otelContext.active(), traceHeaders);

      // 获取tracer
      const tracer = otelTrace.getTracer('http-request');

      // 创建子span
      const spanName = `${request.method} ${request.route?.path || request.url}`;

      return new Observable(subscriber => {
        tracer.startActiveSpan(
          spanName,
          {
            kind: SpanKind.SERVER,
            attributes: {
              'http.method': request.method,
              'http.url': request.url,
              'http.route': request.route?.path || request.url,
              'http.user_agent': request.headers['user-agent'] || '',
              'http.remote_addr': request.ip || request.socket?.remoteAddress || '',
            }
          },
          parentContext,
          (span) => {
            // 在span context中执行后续处理
            const spanContext = otelTrace.setSpan(otelContext.active(), span);

            otelContext.with(spanContext, () => {
              next.handle().subscribe({
                next: (value) => {
                  // 记录响应状态
                  span.setAttributes({
                    'http.status_code': ctx.getResponse().statusCode || 200,
                  });
                  span.setStatus({ code: SpanStatusCode.OK });
                  subscriber.next(value);
                },
                error: (error) => {
                  // 记录错误信息
                  span.setAttributes({
                    'http.status_code': ctx.getResponse().statusCode || 500,
                  });
                  span.setStatus({
                    code: SpanStatusCode.ERROR,
                    message: error.message || 'Internal Server Error'
                  });
                  span.recordException(error);
                  subscriber.error(error);
                },
                complete: () => {
                  span.end();
                  subscriber.complete();
                }
              });
            });
          }
        );
      });

    } catch (error) {
      console.error('Failed to extract trace context:', error);
      // 如果解析失败，继续正常处理
      return next.handle();
    }
  }
}
