import { SimpleSampler } from './simple-sampler';
import { SamplingDecision } from '@opentelemetry/sdk-trace-base';

describe('SimpleSampler', () => {
    let sampler: SimpleSampler;

    beforeEach(() => {
        // 设置测试环境变量
        process.env.TRACER_SAMPLER_RATIO = '0.5';
        sampler = new SimpleSampler();
    });

    afterEach(() => {
        delete process.env.TRACER_SAMPLER_RATIO;
    });

    describe('健康检查请求', () => {
        it('应该跳过健康检查请求', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /health',
                undefined,
                { 'http.target': '/health' }
            );

            expect(result.decision).toBe(SamplingDecision.NOT_RECORD);
        });

        it('应该跳过ping请求', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /ping',
                undefined,
                { 'http.route': '/ping' }
            );

            expect(result.decision).toBe(SamplingDecision.NOT_RECORD);
        });
    });

    describe('HTTP错误', () => {
        it('应该优先采集HTTP 500错误', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                { 'http.status_code': 500 }
            );

            expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
            expect(result.attributes?.['sampling.priority']).toBe('high');
            expect(result.attributes?.['sampling.reason']).toBe('error');
        });

        it('应该优先采集HTTP 404错误', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                { 'http.status_code': 404 }
            );

            expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
            expect(result.attributes?.['sampling.priority']).toBe('high');
        });

        it('应该优先采集带有错误标记的请求', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                { 'error': true }
            );

            expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
            expect(result.attributes?.['sampling.priority']).toBe('high');
        });

        it('应该优先采集带有异常信息的请求', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                { 'exception.type': 'DatabaseError' }
            );

            expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
            expect(result.attributes?.['sampling.priority']).toBe('high');
        });
    });

    describe('正常请求', () => {
        it('应该按采样率处理正常请求', () => {
            // 测试采样器是否工作
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/normal',
                undefined,
                { 'http.status_code': 200 }
            );

            // 应该返回有效的采样决策
            expect([SamplingDecision.RECORD_AND_SAMPLED, SamplingDecision.NOT_RECORD])
                .toContain(result.decision);

            // 如果被采样，应该有正确的属性
            if (result.decision === SamplingDecision.RECORD_AND_SAMPLED) {
                expect(result.attributes?.['sampling.priority']).toBe('normal');
                expect(result.attributes?.['sampling.reason']).toBe('ratio');
            }
        });

        it('应该正确处理没有属性的请求', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'some-operation',
                undefined,
                undefined
            );

            // 应该按正常采样率处理
            expect([SamplingDecision.RECORD_AND_SAMPLED, SamplingDecision.NOT_RECORD])
                .toContain(result.decision);
        });
    });

    describe('配置', () => {
        it('应该使用环境变量中的采样率', () => {
            process.env.TRACER_SAMPLER_RATIO = '0.2';
            const newSampler = new SimpleSampler();

            expect(newSampler.toString()).toContain('0.2');
        });

        it('应该使用默认采样率', () => {
            delete process.env.TRACER_SAMPLER_RATIO;
            const newSampler = new SimpleSampler();

            expect(newSampler.toString()).toContain('0.1');
        });
    });

    describe('边界情况', () => {
        it('应该处理无效的HTTP状态码', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                { 'http.status_code': 'invalid' }
            );

            // 应该按正常采样率处理
            expect([SamplingDecision.RECORD_AND_SAMPLED, SamplingDecision.NOT_RECORD])
                .toContain(result.decision);
        });

        it('应该处理空的属性对象', () => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                'GET /api/test',
                undefined,
                {}
            );

            // 应该按正常采样率处理
            expect([SamplingDecision.RECORD_AND_SAMPLED, SamplingDecision.NOT_RECORD])
                .toContain(result.decision);
        });
    });
});
