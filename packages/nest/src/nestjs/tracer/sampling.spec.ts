import { CustomSampler } from './custom-sampler';
import { PrioritySpanProcessor } from './priority-span-processor';
import { loadSamplingConfig, validateSamplingConfig } from './sampling-config';
import { SamplingDecision } from '@opentelemetry/sdk-trace-base';

describe('Sampling Configuration', () => {
    beforeEach(() => {
        // 清理环境变量
        delete process.env.TRACER_SAMPLER_RATIO;
        delete process.env.TRACER_SLOW_THRESHOLD;
        delete process.env.TRACER_ERROR_WHITELIST;
        delete process.env.TRACER_DB_SAMPLING_MULTIPLIER;
        delete process.env.TRACER_MEDIUM_PRIORITY_RATIO;
        delete process.env.TRACER_LOW_PRIORITY_RATIO;
        delete process.env.TRACER_VERBOSE_LOGGING;
    });

    describe('loadSamplingConfig', () => {
        it('should load default configuration', () => {
            const config = loadSamplingConfig();
            
            expect(config.baseSamplingRatio).toBe(0.1);
            expect(config.slowThreshold).toBe(500);
            expect(config.errorWhitelist).toEqual([]);
            expect(config.databaseSamplingMultiplier).toBe(2.0);
            expect(config.mediumPriorityExportRatio).toBe(0.5);
            expect(config.lowPriorityExportRatio).toBe(0.1);
            expect(config.enableVerboseLogging).toBe(false);
        });

        it('should load configuration from environment variables', () => {
            process.env.TRACER_SAMPLER_RATIO = '0.2';
            process.env.TRACER_SLOW_THRESHOLD = '1000';
            process.env.TRACER_ERROR_WHITELIST = 'TimeoutError,NetworkError';
            process.env.TRACER_DB_SAMPLING_MULTIPLIER = '3.0';
            process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.8';
            process.env.TRACER_LOW_PRIORITY_RATIO = '0.05';
            process.env.TRACER_VERBOSE_LOGGING = 'true';

            const config = loadSamplingConfig();
            
            expect(config.baseSamplingRatio).toBe(0.2);
            expect(config.slowThreshold).toBe(1000);
            expect(config.errorWhitelist).toEqual(['TimeoutError', 'NetworkError']);
            expect(config.databaseSamplingMultiplier).toBe(3.0);
            expect(config.mediumPriorityExportRatio).toBe(0.8);
            expect(config.lowPriorityExportRatio).toBe(0.05);
            expect(config.enableVerboseLogging).toBe(true);
        });
    });

    describe('validateSamplingConfig', () => {
        it('should validate correct configuration', () => {
            const config = loadSamplingConfig();
            const errors = validateSamplingConfig(config);
            expect(errors).toEqual([]);
        });

        it('should detect invalid sampling ratio', () => {
            const config = loadSamplingConfig();
            config.baseSamplingRatio = 1.5;
            
            const errors = validateSamplingConfig(config);
            expect(errors).toContain('baseSamplingRatio must be between 0 and 1');
        });

        it('should detect invalid slow threshold', () => {
            const config = loadSamplingConfig();
            config.slowThreshold = -100;
            
            const errors = validateSamplingConfig(config);
            expect(errors).toContain('slowThreshold must be non-negative');
        });
    });
});

describe('CustomSampler', () => {
    let sampler: CustomSampler;

    beforeEach(() => {
        // 设置测试环境变量
        process.env.TRACER_SAMPLER_RATIO = '0.5';
        process.env.TRACER_SLOW_THRESHOLD = '500';
        process.env.TRACER_ERROR_WHITELIST = 'TimeoutError';
        
        sampler = new CustomSampler();
    });

    afterEach(() => {
        delete process.env.TRACER_SAMPLER_RATIO;
        delete process.env.TRACER_SLOW_THRESHOLD;
        delete process.env.TRACER_ERROR_WHITELIST;
    });

    it('should not record health check requests', () => {
        const result = sampler.shouldSample(
            {} as any,
            'test-trace-id',
            'GET /health',
            undefined,
            { 'http.target': '/health' }
        );
        
        expect(result.decision).toBe(SamplingDecision.NOT_RECORD);
    });

    it('should always sample HTTP errors', () => {
        const result = sampler.shouldSample(
            {} as any,
            'test-trace-id',
            'GET /api/test',
            undefined,
            { 'http.status_code': 500 }
        );
        
        expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
        expect(result.attributes?.['sampling.reason']).toBe('http_error');
    });

    it('should always sample significant errors', () => {
        const result = sampler.shouldSample(
            {} as any,
            'test-trace-id',
            'GET /api/test',
            undefined,
            { 'error': true, 'error.name': 'DatabaseError' }
        );
        
        expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
        expect(result.attributes?.['sampling.reason']).toBe('error');
    });

    it('should skip whitelisted errors', () => {
        const result = sampler.shouldSample(
            {} as any,
            'test-trace-id',
            'GET /api/test',
            undefined,
            { 'error': true, 'error.name': 'TimeoutError' }
        );
        
        // 应该按照正常采样率处理，而不是优先采样
        expect(result.decision).toBeDefined();
    });

    it('should prioritize database operations', () => {
        const result = sampler.shouldSample(
            {} as any,
            'test-trace-id',
            'SELECT * FROM users',
            undefined,
            { 'db.system': 'mysql' }
        );
        
        expect(result.decision).toBeDefined();
        if (result.decision === SamplingDecision.RECORD_AND_SAMPLED) {
            expect(result.attributes?.['sampling.reason']).toBe('database');
        }
    });
});

describe('Integration Test', () => {
    it('should work with realistic configuration', () => {
        // 设置生产环境类似的配置
        process.env.TRACER_SAMPLER_RATIO = '0.1';
        process.env.TRACER_SLOW_THRESHOLD = '500';
        process.env.TRACER_ERROR_WHITELIST = 'TimeoutError,ECONNRESET';
        process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.5';
        process.env.TRACER_LOW_PRIORITY_RATIO = '0.1';

        const sampler = new CustomSampler();
        
        // 测试各种场景
        const scenarios = [
            {
                name: 'Normal request',
                spanName: 'GET /api/users',
                attributes: { 'http.status_code': 200 },
                expectHighPriority: false
            },
            {
                name: 'Error request',
                spanName: 'POST /api/users',
                attributes: { 'http.status_code': 500 },
                expectHighPriority: true
            },
            {
                name: 'Database operation',
                spanName: 'SELECT * FROM users',
                attributes: { 'db.system': 'postgresql' },
                expectHighPriority: false
            },
            {
                name: 'Health check',
                spanName: 'GET /health',
                attributes: { 'http.target': '/health' },
                expectHighPriority: false
            }
        ];

        scenarios.forEach(scenario => {
            const result = sampler.shouldSample(
                {} as any,
                'test-trace-id',
                scenario.spanName,
                undefined,
                scenario.attributes
            );

            if (scenario.name === 'Health check') {
                expect(result.decision).toBe(SamplingDecision.NOT_RECORD);
            } else if (scenario.expectHighPriority) {
                expect(result.decision).toBe(SamplingDecision.RECORD_AND_SAMPLED);
            } else {
                expect(result.decision).toBeDefined();
            }
        });

        // 清理
        delete process.env.TRACER_SAMPLER_RATIO;
        delete process.env.TRACER_SLOW_THRESHOLD;
        delete process.env.TRACER_ERROR_WHITELIST;
        delete process.env.TRACER_MEDIUM_PRIORITY_RATIO;
        delete process.env.TRACER_LOW_PRIORITY_RATIO;
    });
});
