/**
 * 全链路监控采样配置使用示例
 * 
 * 本文件展示了如何在不同场景下配置和使用智能采样功能
 */

import { Tracer } from './otel';
import { loadSamplingConfig, logSamplingConfig, ENV_CONFIG_DOCS } from './sampling-config';

/**
 * 示例1: 生产环境配置
 * 适用于高流量的生产环境，需要平衡性能和可观测性
 */
export function setupProductionTracing() {
    // 设置生产环境的环境变量
    process.env.TRACER_SAMPLER_RATIO = '0.1';           // 10% 基础采样率
    process.env.TRACER_SLOW_THRESHOLD = '500';          // 500ms 慢调用阈值
    process.env.TRACER_ERROR_WHITELIST = 'TimeoutError,ECONNRESET,ENOTFOUND'; // 忽略常见网络错误
    process.env.TRACER_DB_SAMPLING_MULTIPLIER = '2.0';  // 数据库操作2倍采样率
    process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.5';   // 50% 中等优先级导出
    process.env.TRACER_LOW_PRIORITY_RATIO = '0.1';      // 10% 低优先级导出
    process.env.TRACER_VERBOSE_LOGGING = 'false';       // 关闭详细日志

    // 启动链路追踪
    return Tracer.start();
}

/**
 * 示例2: 开发环境配置
 * 适用于开发和测试环境，需要更多的调试信息
 */
export function setupDevelopmentTracing() {
    // 设置开发环境的环境变量
    process.env.TRACER_SAMPLER_RATIO = '0.8';           // 80% 基础采样率
    process.env.TRACER_SLOW_THRESHOLD = '200';          // 200ms 慢调用阈值（更敏感）
    process.env.TRACER_ERROR_WHITELIST = '';            // 不忽略任何错误
    process.env.TRACER_DB_SAMPLING_MULTIPLIER = '1.0';  // 数据库操作正常采样率
    process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.9';   // 90% 中等优先级导出
    process.env.TRACER_LOW_PRIORITY_RATIO = '0.5';      // 50% 低优先级导出
    process.env.TRACER_VERBOSE_LOGGING = 'true';        // 启用详细日志

    // 启动链路追踪
    return Tracer.start();
}

/**
 * 示例3: 高流量场景配置
 * 适用于超高流量场景，需要最小化性能影响
 */
export function setupHighVolumeTracing() {
    // 设置高流量环境的环境变量
    process.env.TRACER_SAMPLER_RATIO = '0.02';          // 2% 基础采样率
    process.env.TRACER_SLOW_THRESHOLD = '1000';         // 1秒慢调用阈值
    process.env.TRACER_ERROR_WHITELIST = 'TimeoutError,ECONNRESET,ENOTFOUND,ETIMEDOUT'; // 忽略更多错误
    process.env.TRACER_DB_SAMPLING_MULTIPLIER = '3.0';  // 数据库操作3倍采样率（仍然重要）
    process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.3';   // 30% 中等优先级导出
    process.env.TRACER_LOW_PRIORITY_RATIO = '0.02';     // 2% 低优先级导出
    process.env.TRACER_VERBOSE_LOGGING = 'false';       // 关闭详细日志

    // 启动链路追踪
    return Tracer.start();
}

/**
 * 示例4: 故障排查配置
 * 适用于故障排查期间，需要最大化数据收集
 */
export function setupTroubleshootingTracing() {
    // 设置故障排查环境的环境变量
    process.env.TRACER_SAMPLER_RATIO = '1.0';           // 100% 基础采样率
    process.env.TRACER_SLOW_THRESHOLD = '100';          // 100ms 慢调用阈值（非常敏感）
    process.env.TRACER_ERROR_WHITELIST = '';            // 不忽略任何错误
    process.env.TRACER_DB_SAMPLING_MULTIPLIER = '1.0';  // 数据库操作正常采样率
    process.env.TRACER_MEDIUM_PRIORITY_RATIO = '1.0';   // 100% 中等优先级导出
    process.env.TRACER_LOW_PRIORITY_RATIO = '1.0';      // 100% 低优先级导出
    process.env.TRACER_VERBOSE_LOGGING = 'true';        // 启用详细日志

    // 启动链路追踪
    return Tracer.start();
}

/**
 * 示例5: 微服务环境配置
 * 适用于微服务架构，需要平衡各服务的采样策略
 */
export function setupMicroserviceTracing(serviceName: string) {
    // 根据服务类型设置不同的配置
    const serviceConfigs = {
        'api-gateway': {
            ratio: '0.2',      // API网关需要更高采样率
            threshold: '300',   // 较低的慢调用阈值
            whitelist: 'TimeoutError,ECONNRESET'
        },
        'user-service': {
            ratio: '0.15',     // 用户服务中等采样率
            threshold: '500',   // 标准慢调用阈值
            whitelist: 'TimeoutError,ValidationError'
        },
        'payment-service': {
            ratio: '0.3',      // 支付服务需要高采样率
            threshold: '200',   // 较低的慢调用阈值
            whitelist: 'TimeoutError'
        },
        'notification-service': {
            ratio: '0.05',     // 通知服务可以低采样率
            threshold: '1000',  // 较高的慢调用阈值
            whitelist: 'TimeoutError,ECONNRESET,DeliveryError'
        }
    };

    const config = serviceConfigs[serviceName] || serviceConfigs['user-service'];

    // 设置环境变量
    process.env.TRACER_SAMPLER_RATIO = config.ratio;
    process.env.TRACER_SLOW_THRESHOLD = config.threshold;
    process.env.TRACER_ERROR_WHITELIST = config.whitelist;
    process.env.TRACER_DB_SAMPLING_MULTIPLIER = '2.0';
    process.env.TRACER_MEDIUM_PRIORITY_RATIO = '0.5';
    process.env.TRACER_LOW_PRIORITY_RATIO = '0.1';
    process.env.TRACER_VERBOSE_LOGGING = 'false';

    // 启动链路追踪
    return Tracer.start();
}

/**
 * 工具函数：打印当前配置
 */
export function printCurrentConfig() {
    console.log('=== 全链路监控采样配置 ===');
    
    const config = loadSamplingConfig();
    logSamplingConfig(config);
    
    console.log('\n=== 环境变量说明 ===');
    Object.entries(ENV_CONFIG_DOCS).forEach(([key, description]) => {
        const currentValue = process.env[key] || '未设置';
        console.log(`${key}: ${description}`);
        console.log(`  当前值: ${currentValue}`);
        console.log('');
    });
}

/**
 * 工具函数：动态调整采样率
 */
export function adjustSamplingRatio(newRatio: number) {
    if (newRatio < 0 || newRatio > 1) {
        throw new Error('采样率必须在 0 到 1 之间');
    }
    
    process.env.TRACER_SAMPLER_RATIO = newRatio.toString();
    console.log(`采样率已调整为: ${(newRatio * 100).toFixed(1)}%`);
    console.log('注意: 需要重启应用才能生效');
}

/**
 * 工具函数：动态调整慢调用阈值
 */
export function adjustSlowThreshold(newThreshold: number) {
    if (newThreshold < 0) {
        throw new Error('慢调用阈值必须为非负数');
    }
    
    process.env.TRACER_SLOW_THRESHOLD = newThreshold.toString();
    console.log(`慢调用阈值已调整为: ${newThreshold}ms`);
    console.log('注意: 需要重启应用才能生效');
}

/**
 * 使用示例
 */
export async function exampleUsage() {
    console.log('=== 全链路监控采样配置示例 ===\n');

    // 根据环境选择配置
    const environment = process.env.NODE_ENV || 'development';
    
    switch (environment) {
        case 'production':
            console.log('使用生产环境配置...');
            await setupProductionTracing();
            break;
        case 'development':
            console.log('使用开发环境配置...');
            await setupDevelopmentTracing();
            break;
        case 'test':
            console.log('使用高流量配置...');
            await setupHighVolumeTracing();
            break;
        default:
            console.log('使用默认配置...');
            await Tracer.start();
    }

    // 打印当前配置
    printCurrentConfig();

    console.log('链路追踪已启动，开始采集数据...');
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    exampleUsage().catch(console.error);
}
