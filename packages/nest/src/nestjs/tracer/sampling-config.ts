/**
 * 全链路监控采样配置
 * 支持环境变量配置和默认值
 */
export interface SamplingConfig {
    /** 基础采样率 (0-1) */
    baseSamplingRatio: number;
    /** 慢调用阈值 (毫秒) */
    slowThreshold: number;
    /** 异常白名单 */
    errorWhitelist: string[];
    /** 数据库操作采样率倍数 */
    databaseSamplingMultiplier: number;
    /** 中等优先级span导出概率 */
    mediumPriorityExportRatio: number;
    /** 低优先级span导出概率 */
    lowPriorityExportRatio: number;
    /** 是否启用详细日志 */
    enableVerboseLogging: boolean;
}

/**
 * 从环境变量加载采样配置
 */
export function loadSamplingConfig(): SamplingConfig {
    return {
        // 基础采样率，默认10%
        baseSamplingRatio: parseFloat(process.env.TRACER_SAMPLER_RATIO || '0.1'),
        
        // 慢调用阈值，默认500ms
        slowThreshold: parseInt(process.env.TRACER_SLOW_THRESHOLD || '500', 10),
        
        // 异常白名单，逗号分隔
        errorWhitelist: (process.env.TRACER_ERROR_WHITELIST || '')
            .split(',')
            .map(item => item.trim())
            .filter(item => item),
        
        // 数据库操作采样率倍数，默认2倍
        databaseSamplingMultiplier: parseFloat(process.env.TRACER_DB_SAMPLING_MULTIPLIER || '2.0'),
        
        // 中等优先级span导出概率，默认50%
        mediumPriorityExportRatio: parseFloat(process.env.TRACER_MEDIUM_PRIORITY_RATIO || '0.5'),
        
        // 低优先级span导出概率，默认10%
        lowPriorityExportRatio: parseFloat(process.env.TRACER_LOW_PRIORITY_RATIO || '0.1'),
        
        // 是否启用详细日志，默认关闭
        enableVerboseLogging: process.env.TRACER_VERBOSE_LOGGING === 'true'
    };
}

/**
 * 验证采样配置
 */
export function validateSamplingConfig(config: SamplingConfig): string[] {
    const errors: string[] = [];
    
    if (config.baseSamplingRatio < 0 || config.baseSamplingRatio > 1) {
        errors.push('baseSamplingRatio must be between 0 and 1');
    }
    
    if (config.slowThreshold < 0) {
        errors.push('slowThreshold must be non-negative');
    }
    
    if (config.databaseSamplingMultiplier < 1) {
        errors.push('databaseSamplingMultiplier must be >= 1');
    }
    
    if (config.mediumPriorityExportRatio < 0 || config.mediumPriorityExportRatio > 1) {
        errors.push('mediumPriorityExportRatio must be between 0 and 1');
    }
    
    if (config.lowPriorityExportRatio < 0 || config.lowPriorityExportRatio > 1) {
        errors.push('lowPriorityExportRatio must be between 0 and 1');
    }
    
    return errors;
}

/**
 * 打印采样配置信息
 */
export function logSamplingConfig(config: SamplingConfig): void {
    console.log('[Tracer] Sampling Configuration:');
    console.log(`  Base Sampling Ratio: ${(config.baseSamplingRatio * 100).toFixed(1)}%`);
    console.log(`  Slow Threshold: ${config.slowThreshold}ms`);
    console.log(`  Error Whitelist: [${config.errorWhitelist.join(', ')}]`);
    console.log(`  Database Sampling Multiplier: ${config.databaseSamplingMultiplier}x`);
    console.log(`  Medium Priority Export Ratio: ${(config.mediumPriorityExportRatio * 100).toFixed(1)}%`);
    console.log(`  Low Priority Export Ratio: ${(config.lowPriorityExportRatio * 100).toFixed(1)}%`);
    console.log(`  Verbose Logging: ${config.enableVerboseLogging ? 'enabled' : 'disabled'}`);
}

/**
 * 环境变量配置说明
 */
export const ENV_CONFIG_DOCS = {
    TRACER_SAMPLER_RATIO: '基础采样率 (0-1)，默认0.1 (10%)',
    TRACER_SLOW_THRESHOLD: '慢调用阈值 (毫秒)，默认500',
    TRACER_ERROR_WHITELIST: '异常白名单，逗号分隔，例如: "TimeoutError,NetworkError"',
    TRACER_DB_SAMPLING_MULTIPLIER: '数据库操作采样率倍数，默认2.0',
    TRACER_MEDIUM_PRIORITY_RATIO: '中等优先级span导出概率 (0-1)，默认0.5',
    TRACER_LOW_PRIORITY_RATIO: '低优先级span导出概率 (0-1)，默认0.1',
    TRACER_VERBOSE_LOGGING: '是否启用详细日志，设置为"true"启用'
};
