# 全链路监控采样配置

本模块提供了智能的全链路监控采样功能，支持设置采样率并优先保证慢调用和异常调用的采集。

## 功能特性

### 🎯 智能采样策略
- **基础采样率**: 可配置的基础采样率，控制整体数据量
- **优先级采样**: 自动优先采集重要的调用链
- **动态调整**: 根据调用类型和状态动态调整采样策略

### 🚨 优先级分类
1. **Critical (关键)**: 错误 + 慢调用 → 100% 采集
2. **High (高)**: 错误/异常调用 → 100% 采集
3. **Medium (中等)**: 慢调用、数据库操作 → 可配置采集率
4. **Low (低)**: 普通调用 → 低采集率

### 🔧 灵活配置
- 支持环境变量配置
- 异常白名单过滤
- 详细日志记录
- 配置验证

## 环境变量配置

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|--------|------|
| `TRACER_SAMPLER_RATIO` | 基础采样率 (0-1) | 0.1 (10%) | 0.2 |
| `TRACER_SLOW_THRESHOLD` | 慢调用阈值 (毫秒) | 500 | 1000 |
| `TRACER_ERROR_WHITELIST` | 异常白名单 (逗号分隔) | 空 | "TimeoutError,NetworkError" |
| `TRACER_DB_SAMPLING_MULTIPLIER` | 数据库操作采样率倍数 | 2.0 | 3.0 |
| `TRACER_MEDIUM_PRIORITY_RATIO` | 中等优先级导出概率 | 0.5 (50%) | 0.8 |
| `TRACER_LOW_PRIORITY_RATIO` | 低优先级导出概率 | 0.1 (10%) | 0.05 |
| `TRACER_VERBOSE_LOGGING` | 启用详细日志 | false | true |

## 使用示例

### 基础配置
```bash
# 设置20%的基础采样率
export TRACER_SAMPLER_RATIO=0.2

# 设置1秒的慢调用阈值
export TRACER_SLOW_THRESHOLD=1000

# 设置异常白名单，忽略超时和网络错误
export TRACER_ERROR_WHITELIST="TimeoutError,NetworkError,ECONNRESET"
```

### 高流量场景配置
```bash
# 低采样率，但保证重要调用
export TRACER_SAMPLER_RATIO=0.05
export TRACER_SLOW_THRESHOLD=300
export TRACER_MEDIUM_PRIORITY_RATIO=0.3
export TRACER_LOW_PRIORITY_RATIO=0.02
```

### 调试场景配置
```bash
# 高采样率，详细日志
export TRACER_SAMPLER_RATIO=0.8
export TRACER_VERBOSE_LOGGING=true
export TRACER_MEDIUM_PRIORITY_RATIO=0.9
export TRACER_LOW_PRIORITY_RATIO=0.5
```

## 采样决策流程

```mermaid
flowchart TD
    A[收到Span] --> B{健康检查?}
    B -->|是| C[不记录]
    B -->|否| D{内部操作?}
    D -->|是| C
    D -->|否| E{HTTP错误?}
    E -->|是| F[高优先级采集]
    E -->|否| G{异常且不在白名单?}
    G -->|是| F
    G -->|否| H{数据库操作?}
    H -->|是| I[中等优先级采集]
    H -->|否| J[按基础采样率采集]
```

## 监控指标

采样器会自动添加以下属性到span中：

- `sampling.priority`: 采样优先级 (high/medium/normal)
- `sampling.reason`: 采样原因 (error/http_error/database/ratio)
- `span.priority`: 处理优先级 (critical/high/medium/low)
- `span.duration_ms`: 调用持续时间
- `span.is_slow`: 是否为慢调用
- `span.processed_at`: 处理时间戳

## 最佳实践

### 1. 生产环境配置
```bash
# 平衡性能和可观测性
export TRACER_SAMPLER_RATIO=0.1
export TRACER_SLOW_THRESHOLD=500
export TRACER_ERROR_WHITELIST="TimeoutError,ECONNRESET"
export TRACER_MEDIUM_PRIORITY_RATIO=0.5
export TRACER_LOW_PRIORITY_RATIO=0.1
```

### 2. 异常白名单设置
- 添加预期的、不重要的异常类型
- 避免过度采集已知的临时性错误
- 定期审查白名单的有效性

### 3. 慢调用阈值调整
- 根据业务特性设置合理阈值
- 考虑不同服务的性能基线
- 监控阈值设置的效果

### 4. 采样率调优
- 从较低采样率开始
- 监控重要调用的覆盖率
- 根据存储和网络成本调整

## 故障排查

### 采样率过低
- 检查 `TRACER_SAMPLER_RATIO` 设置
- 确认重要调用是否被正确分类
- 调整优先级采样率

### 数据量过大
- 降低基础采样率
- 优化异常白名单
- 调整中低优先级采样率

### 重要调用丢失
- 检查慢调用阈值设置
- 确认异常分类逻辑
- 启用详细日志进行调试

## 性能影响

- **CPU开销**: 每个span增加约0.1ms处理时间
- **内存开销**: 每个pending span约1KB内存
- **网络开销**: 根据采样率线性减少
- **存储开销**: 智能采样可减少60-90%的存储需求
