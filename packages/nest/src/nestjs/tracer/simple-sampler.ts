import { Sampler, SamplingDecision, SamplingResult, TraceIdRatioBasedSampler } from '@opentelemetry/sdk-trace-base';
import { Context } from '@opentelemetry/api';

/**
 * 简化的采样器，专注于您的核心需求：
 * 1. 超过1秒的HTTP/远程调用 → 优先采集
 * 2. HTTP/远程调用发生异常 → 优先采集  
 * 3. 其他情况按设置的采样率采集
 */
export class SimpleSampler implements Sampler {
    private ratioSampler: TraceIdRatioBasedSampler;
    private samplingRatio: number;

    constructor() {
        // 从环境变量读取采样率，默认0.1 (10%)
        this.samplingRatio = Number(process.env.TRACER_SAMPLER_RATIO || 0.1);
        this.ratioSampler = new TraceIdRatioBasedSampler(this.samplingRatio);
        
        console.log(`[SimpleSampler] 采样率: ${(this.samplingRatio * 100).toFixed(1)}%，优先采集慢调用(>1s)和异常调用`);
    }

    shouldSample(
        context: Context,
        traceId: string,
        spanName: string,
        _spanKind?: any,
        attributes?: any,
        _links?: any
    ): SamplingResult {
        // 跳过健康检查请求
        if (this.isHealthCheckRequest(spanName, attributes)) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 优先采样：HTTP/远程调用异常
        if (this.isHttpOrRemoteError(attributes)) {
            return {
                decision: SamplingDecision.RECORD_AND_SAMPLED,
                attributes: { 'sampling.priority': 'high', 'sampling.reason': 'error' }
            };
        }

        // 按默认采样率采样其他请求
        const result = this.ratioSampler.shouldSample(context, traceId);
        if (result.decision === SamplingDecision.RECORD_AND_SAMPLED) {
            return {
                ...result,
                attributes: { 'sampling.priority': 'normal', 'sampling.reason': 'ratio' }
            };
        }
        return result;
    }

    /**
     * 判断是否为健康检查请求
     */
    private isHealthCheckRequest(spanName: string, attributes?: any): boolean {
        if (!attributes) return false;
        
        const httpTarget = attributes['http.target'] || attributes['http.url'] || '';
        const httpRoute = attributes['http.route'] || '';
        
        // 常见的健康检查路径
        const healthPaths = ['/health', '/ping', '/status', '/ready', '/live'];
        
        return healthPaths.some(path => 
            httpTarget.includes(path) || 
            httpRoute.includes(path) || 
            spanName.toLowerCase().includes(path)
        );
    }

    /**
     * 判断是否为HTTP或远程调用错误
     */
    private isHttpOrRemoteError(attributes?: any): boolean {
        if (!attributes) return false;

        // 检查HTTP状态码错误
        const httpStatusCode = attributes['http.status_code'];
        if (httpStatusCode && (httpStatusCode >= 400 || httpStatusCode < 200)) {
            return true;
        }

        // 检查是否有错误标记
        if (attributes['error'] === true || attributes['error'] === 'true') {
            return true;
        }

        // 检查异常信息
        const hasException = attributes['exception.type'] || 
                           attributes['exception.message'] || 
                           attributes['error.name'] || 
                           attributes['error.message'];
        
        return !!hasException;
    }

    toString(): string {
        return `SimpleSampler(ratio=${this.samplingRatio})`;
    }
}
