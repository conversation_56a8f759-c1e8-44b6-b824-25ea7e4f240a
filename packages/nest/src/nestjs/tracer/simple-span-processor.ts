import { SpanProcessor, ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';

/**
 * 简化的Span处理器，专注于检测慢调用（>1s）并优先导出
 */
export class SimpleSpanProcessor implements SpanProcessor {
    private pendingSpans: Map<string, { span: ReadableSpan; startTime: number }> = new Map();
    private slowThreshold: number = 1000; // 1秒阈值

    constructor(private exporter: SpanExporter) {
        console.log(`[SimpleSpanProcessor] 慢调用阈值: ${this.slowThreshold}ms`);
    }

    onStart(span: ReadableSpan): void {
        // 记录span开始时间
        this.pendingSpans.set(span.spanContext().spanId, {
            span,
            startTime: Date.now()
        });
    }

    onEnd(span: ReadableSpan): void {
        const spanId = span.spanContext().spanId;
        const pendingSpan = this.pendingSpans.get(spanId);
        
        if (pendingSpan) {
            // 计算持续时间
            const duration = Date.now() - pendingSpan.startTime;
            
            // 检查是否为慢调用
            const isSlowCall = duration > this.slowThreshold;
            const isHttpOrRemote = this.isHttpOrRemoteCall(span);
            
            // 如果是HTTP/远程调用且超过1秒，强制导出
            if (isSlowCall && isHttpOrRemote) {
                console.log(`[SimpleSpanProcessor] 检测到慢调用: ${span.name}, 耗时: ${duration}ms`);
                
                // 添加慢调用标记
                const enhancedSpan = this.enhanceSpanWithSlowCallInfo(span, duration);
                
                // 强制导出
                if (this.exporter.export) {
                    this.exporter.export([enhancedSpan], (result) => {
                        if (result.code !== 0) {
                            console.warn(`[SimpleSpanProcessor] 慢调用导出失败: ${span.name}`, result.error);
                        }
                    });
                }
            } else {
                // 正常处理，让采样器决定
                if (this.exporter.export) {
                    this.exporter.export([span], (result) => {
                        if (result.code !== 0) {
                            console.warn(`[SimpleSpanProcessor] 导出失败: ${span.name}`, result.error);
                        }
                    });
                }
            }
            
            // 清理
            this.pendingSpans.delete(spanId);
        }
    }

    /**
     * 判断是否为HTTP或远程调用
     */
    private isHttpOrRemoteCall(span: ReadableSpan): boolean {
        const attributes = span.attributes;
        
        // 检查HTTP相关属性
        if (attributes['http.method'] || attributes['http.url'] || attributes['http.target']) {
            return true;
        }
        
        // 检查RPC相关属性
        if (attributes['rpc.service'] || attributes['rpc.method']) {
            return true;
        }
        
        // 检查数据库调用
        if (attributes['db.system'] || attributes['db.name']) {
            return true;
        }
        
        // 检查消息队列
        if (attributes['messaging.system'] || attributes['messaging.destination']) {
            return true;
        }
        
        // 根据span名称判断
        const spanName = span.name.toLowerCase();
        const remoteCallPatterns = ['http', 'rpc', 'grpc', 'rest', 'api', 'request', 'call'];
        
        return remoteCallPatterns.some(pattern => spanName.includes(pattern));
    }

    /**
     * 为慢调用span添加额外信息
     */
    private enhanceSpanWithSlowCallInfo(span: ReadableSpan, duration: number): ReadableSpan {
        // 创建增强的span（这里简化处理，实际可能需要更复杂的逻辑）
        const enhancedAttributes = {
            ...span.attributes,
            'span.is_slow': true,
            'span.duration_ms': duration,
            'span.slow_threshold_ms': this.slowThreshold,
            'sampling.priority': 'high',
            'sampling.reason': 'slow_call'
        };

        // 返回带有增强属性的span（注意：这里简化了实现）
        return {
            ...span,
            attributes: enhancedAttributes
        } as ReadableSpan;
    }

    forceFlush(): Promise<void> {
        return Promise.resolve();
    }

    shutdown(): Promise<void> {
        this.pendingSpans.clear();
        return Promise.resolve();
    }
}
