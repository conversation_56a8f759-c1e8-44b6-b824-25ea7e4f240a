import { SpanProcessor, ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';
import { loadSamplingConfig, SamplingConfig } from './sampling-config';

class PrioritySpanProcessor implements SpanProcessor {
    private config: SamplingConfig;
    private pendingSpans: Map<string, ReadableSpan> = new Map();

    constructor(private exporter: SpanExporter) {
        // 加载配置（与采样器保持一致）
        this.config = loadSamplingConfig();
    }

    onStart(span: ReadableSpan): void {
        // 记录开始的span，用于后续检测慢调用
        if (span.spanContext().traceFlags === 1) {
            this.pendingSpans.set(span.spanContext().spanId, span);
        }
    }

    onEnd(span: ReadableSpan): void {
        // 移除pending记录
        this.pendingSpans.delete(span.spanContext().spanId);

        // 只处理已被采样的span（避免处理未采样的span）
        if (span.spanContext().traceFlags === 1) {
            const priority = this.calculateSpanPriority(span);
            const shouldExport = this.shouldExportSpan(span, priority);

            // 导出满足条件的span
            if (shouldExport && this.exporter.export) {
                // 添加优先级和处理时间戳到span属性
                const enhancedSpan = this.enhanceSpanWithMetadata(span, priority);

                if (this.config.enableVerboseLogging) {
                    const duration = this.getSpanDuration(span);
                    console.log(`[PrioritySpanProcessor] Exporting span: ${span.name}, priority: ${priority}, duration: ${duration}ms`);
                }

                this.exporter.export([enhancedSpan], (result) => {
                    if (result.code !== 0) {
                        console.warn(`[PrioritySpanProcessor] Export failed for span ${span.name}:`, result.error);
                    }
                });
            } else if (this.config.enableVerboseLogging) {
                const duration = this.getSpanDuration(span);
                console.log(`[PrioritySpanProcessor] Skipping span: ${span.name}, priority: ${priority}, duration: ${duration}ms`);
            }
        }
    }

    /**
     * 计算span的优先级
     */
    private calculateSpanPriority(span: ReadableSpan): 'critical' | 'high' | 'medium' | 'low' {
        const duration = this.getSpanDuration(span);
        const isError = span.status.code !== 0;
        const hasException = this.hasException(span);
        const isSlowCall = duration > this.config.slowThreshold;

        // 关键优先级：错误 + 慢调用
        if ((isError || hasException) && isSlowCall) {
            return 'critical';
        }

        // 高优先级：错误或异常（不在白名单中）
        if (isError || hasException) {
            const errorName = this.getErrorName(span);
            const isWhitelisted = this.config.errorWhitelist.some(item =>
                errorName.toLowerCase().includes(item.toLowerCase())
            );
            if (!isWhitelisted) {
                return 'high';
            }
        }

        // 高优先级：非常慢的调用（超过阈值2倍）
        if (duration > this.config.slowThreshold * 2) {
            return 'high';
        }

        // 中等优先级：慢调用
        if (isSlowCall) {
            return 'medium';
        }

        // 中等优先级：数据库操作
        if (this.isDatabaseOperation(span)) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * 判断是否应该导出span
     */
    private shouldExportSpan(_span: ReadableSpan, priority: string): boolean {
        // 总是导出关键和高优先级的span
        if (priority === 'critical' || priority === 'high') {
            return true;
        }

        // 中等优先级：根据配置的采样率决定
        if (priority === 'medium') {
            return Math.random() < this.config.mediumPriorityExportRatio;
        }

        // 低优先级：根据配置的采样率决定
        if (priority === 'low') {
            return Math.random() < this.config.lowPriorityExportRatio;
        }

        return false;
    }

    /**
     * 增强span的元数据
     */
    private enhanceSpanWithMetadata(span: ReadableSpan, priority: string): ReadableSpan {
        const duration = this.getSpanDuration(span);

        // 创建增强的属性
        const enhancedAttributes = {
            ...span.attributes,
            'span.priority': priority,
            'span.duration_ms': duration,
            'span.processed_at': Date.now(),
            'span.is_slow': duration > this.config.slowThreshold,
            'span.slow_threshold_ms': this.config.slowThreshold
        };

        // 返回带有增强属性的span（注意：这里简化处理，实际可能需要创建新的span对象）
        return {
            ...span,
            attributes: enhancedAttributes
        } as ReadableSpan;
    }

    /**
     * 获取span持续时间（毫秒）
     */
    private getSpanDuration(span: ReadableSpan): number {
        if (!span.startTime || !span.endTime) return 0;

        const start = span.startTime[0] * 1e3 + span.startTime[1] / 1e6;
        const end = span.endTime[0] * 1e3 + span.endTime[1] / 1e6;
        return end - start;
    }

    /**
     * 检查span是否有异常
     */
    private hasException(span: ReadableSpan): boolean {
        const attributes = span.attributes;
        return !!(attributes['exception.type'] ||
            attributes['exception.message'] ||
            attributes['error'] ||
            attributes['error.name'] ||
            attributes['error.message']);
    }

    /**
     * 获取错误名称
     */
    private getErrorName(span: ReadableSpan): string {
        const attributes = span.attributes;
        return (attributes['error.name'] ||
            attributes['exception.type'] ||
            attributes['error.type'] ||
            '') as string;
    }

    /**
     * 检查是否为数据库操作
     */
    private isDatabaseOperation(span: ReadableSpan): boolean {
        const attributes = span.attributes;
        return !!(attributes['db.system'] ||
            attributes['db.operation'] ||
            attributes['db.statement'] ||
            span.name.match(/^(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)/i));
    }

    shutdown(): Promise<void> {
        // 清理pending spans
        this.pendingSpans.clear();

        if (typeof this.exporter.shutdown === 'function') {
            return this.exporter.shutdown();
        }
        return Promise.resolve();
    }

    forceFlush(): Promise<void> {
        if (typeof this.exporter.forceFlush === 'function') {
            return this.exporter.forceFlush();
        }
        return Promise.resolve();
    }
}

export { PrioritySpanProcessor };