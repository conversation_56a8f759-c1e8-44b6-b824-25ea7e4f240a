import { SpanProcessor, ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';

class PrioritySpanProcessor implements SpanProcessor {
    private slowThreshold: number;
    private errorWhitelist: string[] = [];

    constructor(private exporter: SpanExporter) {
        // 从环境变量获取慢调用阈值，默认500ms（与采样器保持一致）
        this.slowThreshold = Number(process.env.TRACER_SLOW_THRESHOLD || 500);
        // 从环境变量获取异常白名单，逗号分隔
        const whitelist = process.env.TRACER_ERROR_WHITELIST || '';
        if (whitelist) {
            this.errorWhitelist = whitelist.split(',');
        }
    }

    onStart(span: ReadableSpan): void { }

    onEnd(span: ReadableSpan): void {
        // 只处理已被采样的span（避免处理未采样的span）
        if (span.spanContext().traceFlags === 1) {
            const start = span.startTime ? span.startTime[0] * 1e3 + span.startTime[1] / 1e6 : 0;
            const end = span.endTime ? span.endTime[0] * 1e3 + span.endTime[1] / 1e6 : 0;
            const duration = end - start; // 耗时（毫秒）
            const isError = span.status.code !== 0; // 是否有错误

            // 如果是错误且不在白名单中，或者超过慢阈值，则导出
            let shouldExport = duration > this.slowThreshold;

            if (isError) {
                const errorName = span.attributes['error.name'] || '';
                const isWhitelisted = this.errorWhitelist.some(item => (errorName as string).includes(item));
                if (!isWhitelisted) {
                    shouldExport = true;
                }
            }

            // 导出满足条件的span
            if (shouldExport && this.exporter.export) {
                this.exporter.export([span], () => { });
            }
        }
    }

    shutdown(): Promise<void> {
        if (typeof this.exporter.shutdown === 'function') {
            return this.exporter.shutdown();
        }
        return Promise.resolve();
    }

    forceFlush(): Promise<void> {
        if (typeof this.exporter.forceFlush === 'function') {
            return this.exporter.forceFlush();
        }
        return Promise.resolve();
    }
}

export { PrioritySpanProcessor };