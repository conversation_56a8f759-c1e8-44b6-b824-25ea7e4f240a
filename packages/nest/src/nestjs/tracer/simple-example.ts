/**
 * 简化的全链路监控采样配置使用示例
 * 
 * 专注于您的核心需求：
 * 1. 超过1秒的HTTP/远程调用 → 优先采集
 * 2. HTTP/远程调用发生异常 → 优先采集  
 * 3. 其他情况按设置的采样率采集
 */

import { Tracer } from './otel';

/**
 * 生产环境配置
 * 适用于高流量的生产环境
 */
export function setupProductionTracing() {
    // 设置10%的基础采样率
    process.env.TRACER_SAMPLER_RATIO = '0.1';
    
    // 启动链路追踪
    return Tracer.start();
}

/**
 * 开发环境配置
 * 适用于开发和测试环境
 */
export function setupDevelopmentTracing() {
    // 设置50%的基础采样率，便于调试
    process.env.TRACER_SAMPLER_RATIO = '0.5';
    
    // 启动链路追踪
    return Tracer.start();
}

/**
 * 高流量场景配置
 * 适用于超高流量场景，需要最小化性能影响
 */
export function setupHighVolumeTracing() {
    // 设置2%的基础采样率
    process.env.TRACER_SAMPLER_RATIO = '0.02';
    
    // 启动链路追踪
    return Tracer.start();
}

/**
 * 故障排查配置
 * 适用于故障排查期间，需要最大化数据收集
 */
export function setupTroubleshootingTracing() {
    // 设置100%的基础采样率
    process.env.TRACER_SAMPLER_RATIO = '1.0';
    
    // 启动链路追踪
    return Tracer.start();
}

/**
 * 工具函数：动态调整采样率
 */
export function adjustSamplingRatio(newRatio: number) {
    if (newRatio < 0 || newRatio > 1) {
        throw new Error('采样率必须在 0 到 1 之间');
    }
    
    process.env.TRACER_SAMPLER_RATIO = newRatio.toString();
    console.log(`采样率已调整为: ${(newRatio * 100).toFixed(1)}%`);
    console.log('注意: 需要重启应用才能生效');
}

/**
 * 打印当前配置
 */
export function printCurrentConfig() {
    const samplingRatio = Number(process.env.TRACER_SAMPLER_RATIO || 0.1);
    
    console.log('=== 简化全链路监控采样配置 ===');
    console.log(`基础采样率: ${(samplingRatio * 100).toFixed(1)}%`);
    console.log('优先采集规则:');
    console.log('  1. 超过1秒的HTTP/远程调用 → 100%采集');
    console.log('  2. HTTP/远程调用发生异常 → 100%采集');
    console.log('  3. 其他情况按基础采样率采集');
    console.log('');
    console.log('环境变量配置:');
    console.log(`  TRACER_SAMPLER_RATIO: ${process.env.TRACER_SAMPLER_RATIO || '0.1 (默认)'}`);
    console.log('');
}

/**
 * 使用示例
 */
export async function exampleUsage() {
    console.log('=== 简化全链路监控采样配置示例 ===\n');

    // 根据环境选择配置
    const environment = process.env.NODE_ENV || 'development';
    
    switch (environment) {
        case 'production':
            console.log('使用生产环境配置...');
            await setupProductionTracing();
            break;
        case 'development':
            console.log('使用开发环境配置...');
            await setupDevelopmentTracing();
            break;
        case 'test':
            console.log('使用高流量配置...');
            await setupHighVolumeTracing();
            break;
        default:
            console.log('使用默认配置...');
            await Tracer.start();
    }

    // 打印当前配置
    printCurrentConfig();

    console.log('链路追踪已启动，开始采集数据...');
    console.log('');
    console.log('采集策略:');
    console.log('✅ 所有HTTP/远程调用异常都会被采集');
    console.log('✅ 所有超过1秒的HTTP/远程调用都会被采集');
    console.log(`📊 其他调用按 ${(Number(process.env.TRACER_SAMPLER_RATIO || 0.1) * 100).toFixed(1)}% 采样率采集`);
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    exampleUsage().catch(console.error);
}
