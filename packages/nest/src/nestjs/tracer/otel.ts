import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { J<PERSON>gerPropagator } from '@opentelemetry/propagator-jaeger';
import { CompositePropagator, W3CTraceContextPropagator, W3CBaggagePropagator } from '@opentelemetry/core';
import { SimpleSampler } from './simple-sampler';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';
import { SimpleSpanProcessor } from './simple-span-processor';

// 自动追踪配置
const instrumentations = getNodeAutoInstrumentations({
  // 禁用 文件系统追踪
  '@opentelemetry/instrumentation-fs': { enabled: false },
  // 禁用 TCP 连接追踪
  '@opentelemetry/instrumentation-net': { enabled: false },
  // 禁用 DNS 解析追踪
  '@opentelemetry/instrumentation-dns': { enabled: false },
  // 禁用 缓存池
  '@opentelemetry/instrumentation-generic-pool': { enabled: false },
  // 禁用 LRU 缓存
  '@opentelemetry/instrumentation-lru-memoizer': { enabled: false },
  // 禁用 Pino 日志库的日志链路
  '@opentelemetry/instrumentation-pino': { enabled: false },
  // 禁用 Winston 日志库的日志链路
  '@opentelemetry/instrumentation-winston': { enabled: false },
  // 禁用 Memcached 缓存操作的链路
  '@opentelemetry/instrumentation-memcached': { enabled: false },
  // 禁用 Cassandra 数据库操作的链路
  '@opentelemetry/instrumentation-cassandra-driver': { enabled: false },
  // 禁用 Tedious（SQL Server 的 Node.js 驱动）数据库操作的链路
  '@opentelemetry/instrumentation-tedious': { enabled: false },
  // 禁用 Restify 框架的 HTTP 请求/响应链路
  '@opentelemetry/instrumentation-restify': { enabled: false },
  // 禁用 Socket.io 实时通信的链路
  '@opentelemetry/instrumentation-socket.io': { enabled: false },
  // 禁用 Cucumber 行为驱动测试的链路
  '@opentelemetry/instrumentation-cucumber': { enabled: false },
  // 其他 instrumentations 保持默认（enabled: true）
});

// 链路追踪上报地址配置
const exporter = new OTLPTraceExporter({
  url: process.env.TRACER_INTERNAL_REPORT == 'Y' ? process.env.HTTP_INTERNAL_TRACES_ENDPOINT : process.env.HTTP_TRACES_ENDPOINT,
});

// 使用简化采样器 - 支持采样率配置，优先采集慢调用和异常调用
const sampler = new SimpleSampler();

// 简化处理器配置
const spanProcessor = new SimpleSpanProcessor(exporter);

// SDK配置
const sdk = new NodeSDK({
  serviceName: process.env.TRACER_SERVICE_NAME,
  instrumentations: [instrumentations],
  sampler: sampler, // 先通过采样器判断
  spanProcessor: spanProcessor, // 后通过处理器二次筛选
  textMapPropagator: new CompositePropagator({
    propagators: [
      new JaegerPropagator(), // 支持uber-trace-id
      new W3CTraceContextPropagator(), // 支持标准的trace context
      new W3CBaggagePropagator(), // 支持baggage传播
    ],
  }),
});

export const Tracer = {
  start: () => {
    // 不开启链路调用
    if (process.env.DISABLE_TRACER_REPORT === 'Y') return Promise.resolve();
    // 环境变量校验
    const errors: string[] = [];
    if (!process.env.TRACER_SERVICE_NAME) errors.push('TRACER_SERVICE_NAME');
    if (process.env.TRACER_INTERNAL_REPORT === 'Y') {
      if (!process.env.HTTP_INTERNAL_TRACES_ENDPOINT) errors.push('HTTP_INTERNAL_TRACES_ENDPOINT');
    } else {
      if (!process.env.HTTP_TRACES_ENDPOINT) errors.push('HTTP_TRACES_ENDPOINT');
    }
    if (errors.length > 0) {
      console.error(`[Tracing] 缺少必要环境变量: ${errors.join(', ')}`);
      // 可以选择抛出异常，也可以直接 return，不启动链路追踪
      return Promise.resolve(); // 或者 throw new Error(...)
    }
    return sdk.start();
  },
  shutdown: () => {
    // 这里无需校验 env，直接优雅关闭
    return sdk.shutdown();
  },
};