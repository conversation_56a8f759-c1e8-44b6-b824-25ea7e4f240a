import { <PERSON><PERSON>, <PERSON>plingDec<PERSON>, SamplingResult, TraceIdRatioBasedSampler, AlwaysOnSampler } from '@opentelemetry/sdk-trace-base';
import { Context } from '@opentelemetry/api';

export class CustomSampler implements Sampler {
    private ratioSampler: TraceIdRatioBasedSampler;
    private alwaysOnSampler: AlwaysOnSampler;
    // 从环境变量读取慢调用阈值，默认500ms
    private slowThreshold: number;
    // 异常类名白名单数组
    private errorWhitelist: string[] = [];

    constructor() {
        // 使用环境变量设置采样率，默认为0.1
        this.ratioSampler = new TraceIdRatioBasedSampler(Number(process.env.TRACER_SAMPLER_RATIO || 0.1));
        this.alwaysOnSampler = new AlwaysOnSampler();
        // 从环境变量获取慢调用阈值，默认500ms
        this.slowThreshold = Number(process.env.TRACER_SLOW_THRESHOLD || 500);
        // 从环境变量获取异常白名单，逗号分隔
        const whitelist = process.env.TRACER_ERROR_WHITELIST || '';
        if (whitelist) {
            this.errorWhitelist = whitelist.split(',');
        }
    }

    shouldSample(
        context: Context,
        traceId: string,
        spanName: string,
        spanKind?: any,
        attributes?: any,
        links?: any
    ): SamplingResult {
        // 过滤健康检查请求
        if ((spanName === 'GET' || spanName === 'POST') && attributes && (attributes['http.target'] === '/health' || attributes['http.route'] === '/health'))
            return { decision: SamplingDecision.NOT_RECORD };
        if (spanName.includes('/health'))
            return { decision: SamplingDecision.NOT_RECORD };

        // 过滤中间件
        if (spanName.startsWith('middleware - ') || spanName.startsWith('getEnvMiddleware'))
            return { decision: SamplingDecision.NOT_RECORD };

        // 优先采样HTTP错误（状态码 >= 400 或 < 200）
        if (attributes && attributes['http.status_code']) {
            const statusCode = attributes['http.status_code'];
            if (statusCode >= 400 || statusCode < 200) {
                return { decision: SamplingDecision.RECORD_AND_SAMPLED };
            }
        }

        // 优先采样带有错误的span
        if (attributes && attributes['error']) {
            const errorName = attributes['error.name'] || '';
            // 检查错误是否在白名单中
            const isWhitelisted = this.errorWhitelist.some(item => errorName.includes(item));
            if (!isWhitelisted) {
                return { decision: SamplingDecision.RECORD_AND_SAMPLED };
            }
        }

        // 按默认采样率采样
        return this.ratioSampler.shouldSample(context, traceId);
    }

    toString(): string {
        return `CustomSampler(ratio=${process.env.TRACER_SAMPLER_RATIO || 0.1}, slowThreshold=${this.slowThreshold}ms)`;
    }
}