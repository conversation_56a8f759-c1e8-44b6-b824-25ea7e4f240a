import { <PERSON><PERSON>, <PERSON>plingDec<PERSON>, SamplingResult, TraceIdRatioBasedSampler } from '@opentelemetry/sdk-trace-base';
import { Context } from '@opentelemetry/api';
import { loadSamplingConfig, validateSamplingConfig, logSamplingConfig, SamplingConfig } from './sampling-config';

export class CustomSampler implements Sampler {
    private ratioSampler: TraceIdRatioBasedSampler;
    private config: SamplingConfig;

    constructor() {
        // 加载和验证配置
        this.config = loadSamplingConfig();
        const configErrors = validateSamplingConfig(this.config);

        if (configErrors.length > 0) {
            console.error('[CustomSampler] Configuration errors:', configErrors);
            throw new Error(`Invalid sampling configuration: ${configErrors.join(', ')}`);
        }

        // 初始化采样器
        this.ratioSampler = new TraceIdRatioBasedSampler(this.config.baseSamplingRatio);

        // 打印配置信息
        if (this.config.enableVerboseLogging) {
            logSamplingConfig(this.config);
        }
    }

    shouldSample(
        context: Context,
        traceId: string,
        spanName: string,
        _spanKind?: any,
        attributes?: any,
        _links?: any
    ): SamplingResult {
        // 过滤健康检查请求 - 完全不记录
        if (this.isHealthCheckRequest(spanName, attributes)) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 过滤中间件和内部操作 - 完全不记录
        if (this.isInternalOperation(spanName)) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 优先采样：HTTP错误（状态码 >= 400 或 < 200）
        if (this.isHttpError(attributes)) {
            return {
                decision: SamplingDecision.RECORD_AND_SAMPLED,
                attributes: { 'sampling.priority': 'high', 'sampling.reason': 'http_error' }
            };
        }

        // 优先采样：带有错误的span（不在白名单中的错误）
        if (this.isSignificantError(attributes)) {
            return {
                decision: SamplingDecision.RECORD_AND_SAMPLED,
                attributes: { 'sampling.priority': 'high', 'sampling.reason': 'error' }
            };
        }

        // 优先采样：数据库操作（通常比较重要）
        if (this.isDatabaseOperation(spanName, attributes)) {
            // 数据库操作使用更高的采样率
            const dbSamplingRatio = Math.min(this.config.baseSamplingRatio * this.config.databaseSamplingMultiplier, 1.0);
            const dbSampler = new TraceIdRatioBasedSampler(dbSamplingRatio);
            const result = dbSampler.shouldSample(context, traceId);
            if (result.decision === SamplingDecision.RECORD_AND_SAMPLED) {
                return {
                    ...result,
                    attributes: { 'sampling.priority': 'medium', 'sampling.reason': 'database' }
                };
            }
            return result;
        }

        // 按默认采样率采样其他请求
        const result = this.ratioSampler.shouldSample(context, traceId);
        if (result.decision === SamplingDecision.RECORD_AND_SAMPLED) {
            return {
                ...result,
                attributes: { 'sampling.priority': 'normal', 'sampling.reason': 'ratio' }
            };
        }
        return result;
    }

    /**
     * 检查是否为健康检查请求
     */
    private isHealthCheckRequest(spanName: string, attributes?: any): boolean {
        // 检查span名称
        if (spanName.includes('/health')) {
            return true;
        }

        // 检查HTTP属性
        if (attributes) {
            const target = attributes['http.target'];
            const route = attributes['http.route'];
            const url = attributes['http.url'];

            if (target === '/health' || route === '/health' ||
                (url && typeof url === 'string' && url.includes('/health'))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为内部操作（中间件等）
     */
    private isInternalOperation(spanName: string): boolean {
        const internalPrefixes = [
            'middleware - ',
            'getEnvMiddleware',
            'cors',
            'helmet',
            'compression',
            'express-session'
        ];

        return internalPrefixes.some(prefix => spanName.startsWith(prefix));
    }

    /**
     * 检查是否为HTTP错误
     */
    private isHttpError(attributes?: any): boolean {
        if (!attributes) return false;

        const statusCode = attributes['http.status_code'];
        if (typeof statusCode === 'number') {
            return statusCode >= 400 || statusCode < 200;
        }

        return false;
    }

    /**
     * 检查是否为重要错误（不在白名单中的错误）
     */
    private isSignificantError(attributes?: any): boolean {
        if (!attributes || !attributes['error']) return false;

        const errorName = attributes['error.name'] || '';
        const errorType = attributes['error.type'] || '';
        const exception = attributes['exception.type'] || '';

        // 检查是否在白名单中
        const errorToCheck = errorName || errorType || exception;
        if (!errorToCheck) return false;

        const isWhitelisted = this.config.errorWhitelist.some(item =>
            errorToCheck.toLowerCase().includes(item.toLowerCase())
        );

        return !isWhitelisted;
    }

    /**
     * 检查是否为数据库操作
     */
    private isDatabaseOperation(spanName: string, attributes?: any): boolean {
        // 检查span名称
        const dbOperationPatterns = [
            /^(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)/i,
            /redis/i,
            /mongodb/i,
            /mysql/i,
            /postgresql/i,
            /sqlite/i
        ];

        if (dbOperationPatterns.some(pattern => pattern.test(spanName))) {
            return true;
        }

        // 检查属性
        if (attributes) {
            const dbSystem = attributes['db.system'];
            const dbOperation = attributes['db.operation'];
            const dbStatement = attributes['db.statement'];

            if (dbSystem || dbOperation || dbStatement) {
                return true;
            }
        }

        return false;
    }

    toString(): string {
        return `CustomSampler(ratio=${this.config.baseSamplingRatio}, slowThreshold=${this.config.slowThreshold}ms, errorWhitelist=[${this.config.errorWhitelist.join(', ')}])`;
    }
}