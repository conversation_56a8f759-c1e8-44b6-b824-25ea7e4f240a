# 简化全链路监控采样配置

根据您的需求，我们提供了一个简化的采样方案，专注于核心功能：

## 🎯 核心功能

### 采样策略
1. **超过1秒的HTTP/远程调用** → 100% 采集
2. **HTTP/远程调用发生异常** → 100% 采集  
3. **其他情况** → 按设置的采样率采集

### 特点
- ✅ 简单易用，只需设置一个环境变量
- ✅ 自动识别慢调用（>1秒）并优先采集
- ✅ 自动识别异常调用并优先采集
- ✅ 跳过健康检查请求，减少无用数据
- ✅ 整体采样率可控，平衡性能和可观测性

## 🔧 配置

### 环境变量
只需要设置一个环境变量：

```bash
# 设置基础采样率（0-1之间的小数）
export TRACER_SAMPLER_RATIO=0.1  # 10%采样率
```

### 配置示例

#### 生产环境（推荐）
```bash
export TRACER_SAMPLER_RATIO=0.1  # 10%基础采样率
```

#### 开发环境
```bash
export TRACER_SAMPLER_RATIO=0.5  # 50%基础采样率，便于调试
```

#### 高流量环境
```bash
export TRACER_SAMPLER_RATIO=0.02  # 2%基础采样率，减少数据量
```

#### 故障排查
```bash
export TRACER_SAMPLER_RATIO=1.0  # 100%采样率，收集所有数据
```

## 🚀 使用方法

### 1. 基础使用
```typescript
import { Tracer } from '@yqz/nest/tracer';

// 设置采样率
process.env.TRACER_SAMPLER_RATIO = '0.1';

// 启动链路追踪
await Tracer.start();
```

### 2. 不同环境配置
```typescript
import { 
    setupProductionTracing,
    setupDevelopmentTracing,
    setupHighVolumeTracing,
    setupTroubleshootingTracing
} from '@yqz/nest/tracer/simple-example';

// 根据环境选择配置
const environment = process.env.NODE_ENV;

switch (environment) {
    case 'production':
        await setupProductionTracing();
        break;
    case 'development':
        await setupDevelopmentTracing();
        break;
    case 'test':
        await setupHighVolumeTracing();
        break;
    default:
        await setupTroubleshootingTracing();
}
```

### 3. 动态调整采样率
```typescript
import { adjustSamplingRatio, printCurrentConfig } from '@yqz/nest/tracer/simple-example';

// 查看当前配置
printCurrentConfig();

// 动态调整采样率（需要重启应用生效）
adjustSamplingRatio(0.2); // 调整为20%
```

## 📊 采样决策流程

```
收到Span
    ↓
是健康检查? → 是 → 不记录
    ↓ 否
HTTP/远程调用异常? → 是 → 100%采集（高优先级）
    ↓ 否
超过1秒的HTTP/远程调用? → 是 → 100%采集（高优先级）
    ↓ 否
按基础采样率采集（正常优先级）
```

## 🔍 监控指标

采样器会自动添加以下属性到span中：

- `sampling.priority`: 采样优先级
  - `high`: 错误或慢调用
  - `normal`: 正常采样
- `sampling.reason`: 采样原因
  - `error`: 异常调用
  - `slow_call`: 慢调用（>1秒）
  - `ratio`: 按采样率采集

## 📈 效果预期

### 数据量控制
- **基础采样率10%**: 正常调用只采集10%
- **重要调用100%**: 所有错误和慢调用都会被采集
- **整体减少**: 通常可减少70-90%的数据量

### 可观测性保障
- ✅ 所有异常都会被捕获
- ✅ 所有慢调用都会被捕获
- ✅ 重要问题不会遗漏
- ✅ 性能问题可及时发现

## 🛠️ 故障排查

### 采样率过低
```bash
# 临时提高采样率
export TRACER_SAMPLER_RATIO=0.5
# 重启应用
```

### 数据量过大
```bash
# 降低采样率
export TRACER_SAMPLER_RATIO=0.05
# 重启应用
```

### 重要调用丢失
- 检查是否为HTTP/远程调用
- 确认调用时间是否超过1秒
- 确认是否有异常发生

## 🔧 技术实现

### 核心组件
1. **SimpleSampler**: 简化的采样器，实现核心采样逻辑
2. **SimpleSpanProcessor**: 简化的处理器，检测慢调用并强制导出

### 关键特性
- 自动检测HTTP状态码错误（>=400 或 <200）
- 自动检测异常属性（error、exception.type等）
- 自动检测慢调用（持续时间>1秒）
- 自动跳过健康检查请求（/health、/ping等）

## 📝 最佳实践

1. **生产环境建议采样率**: 5-15%
2. **开发环境建议采样率**: 30-80%
3. **定期监控采样效果**: 观察重要调用覆盖率
4. **根据业务调整**: 不同服务可设置不同采样率

这个简化版本专注于您的核心需求，去除了复杂的配置，使用简单但有效。
